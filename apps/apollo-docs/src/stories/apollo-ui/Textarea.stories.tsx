import {
  Button,
  IconButton,
  Input,
  Modal,
  Select,
  Textarea,
  Typography,
} from "@apollo/ui";
import { InfoCircle } from "@design-systems/apollo-icons";
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { useState } from "react";
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components";

/**
 * Textarea component
 *
 * The Textarea component provides a styled, accessible multi-line text input with optional label,
 * helper text, decorators, required indicator, error state, full‑width layout, character count support,
 * and auto-resizing capabilities.
 *
 * Notes:
 * - Default size is "medium";
 * - Available sizes: "small" | "medium";
 * - Default minRows is 3;
 * - Supports auto-resizing with minRows and maxRows.
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Textarea",
  component: Textarea,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/gdmbYIRxMhNlIe0oNtSprm/%F0%9F%92%99-Apollo-Alias-Foundations-and-Styles?node-id=2659-10955&m=dev",
    },
    docs: {
      description: {
        component:
          "The Textarea component renders a multi-line text input with Apollo design system styling. It supports auto-resizing and character counting.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { Textarea } from "@apollo/ui"`}
            language="tsx"
          />
          <h2 id="textarea-props">Props</h2>
          <ArgTypes />
          <h2 id="textarea-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use text area to allow users to write multiple lines of text, usually for comments or descriptions.",
              "Be clearly labeled so it's obvious to users what they should enter into the field",
              "Helper text should be used to provide additional information or instructions.",
              "Only ask for information that's really needed",
              "Inline error messages should be used to provide feedback on invalid input.",
              "Character or word counters should be used if there is a character or word limit.",
              "Use appropriate minRows and maxRows to control the textarea size and auto-resizing behavior.",
            ]}
          />
          <h2 id="textarea-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always use <code>label</code> prop to provide a text label for
                the textarea. The label should be descriptive and concise,
                indicating what information the user should enter.
              </>,
              <>
                Use the <code>placeholder</code> prop to provide a short hint
                that describes the expected value of the textarea. The
                placeholder should be a short phrase that helps the user
                understand what to enter.
              </>,
              <>
                <code>labelDecorator</code> or <code>helperTextDecorator</code>{" "}
                should be used to provide additional information or
                instructions.
              </>,
              <>
                Use the <code>helperText</code> prop to provide additional
                information or instructions. The helper text should be concise
                and to the point.
              </>,
              <>
                Provide descriptive error messages using <code>error</code> prop
                with <code>helperText</code> to help users understand and
                correct validation issues.
              </>,
              <>
                For required fields, use the <code>required</code> prop to
                ensure proper screen reader announcements and native browser
                validation.
              </>,
              <>
                Character counting with <code>hasCharacterCount</code> with{" "}
                <code>maxLength</code> prop automatically provides accessibility
                labels for screen readers.
              </>,
              <>
                Use <code>minRows</code> and <code>maxRows</code> to control the
                initial size and maximum height of the textarea for better user
                experience.
              </>,
            ]}
          />
          <h2 id="textarea-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Textarea component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloTextarea-fieldRoot",
                description: "Styles applied to the field wrapper element",
                usageNotes:
                  "Use for overall field styling including label and helper text positioning",
              },
              {
                cssClassName: ".ApolloTextarea-controlRoot",
                description: "Styles applied to the textarea control container",
                usageNotes:
                  "Contains the textarea element, handles border and background",
              },
              {
                cssClassName: ".ApolloTextarea-textarea",
                description: "Styles applied to the textarea element itself",
                usageNotes: "Use for styling the actual textarea input element",
              },
            ]}
          />
          <h2 id="textarea-examples">Examples</h2>
          <Stories title="" />
          <h2 id="textarea-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Textarea
                        label="Description"
                        placeholder="Enter your description"
                      />
                    </div>
                  ),
                  description: "Keep the placeholder for textarea hints only",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Textarea
                        label="Description"
                        placeholder="Must be at least 50 characters long"
                      />
                    </div>
                  ),
                  description:
                    "Do not misuse placeholder as helper text, use helperText instead.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Textarea
                        label="Comments"
                        placeholder="Share your thoughts..."
                      />
                    </div>
                  ),
                  description:
                    "Make sure your textarea has a short, descriptive label above it that describes what the user should type into the box below.",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Textarea
                        label="What are your thoughts about this product? Please provide detailed feedback."
                        placeholder="Share your thoughts..."
                      />
                    </div>
                  ),
                  description:
                    "Avoid phrasing your labels as questions. Keep in mind that your label shouldn't contain instructions. Reserve those for the helper text.",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Textarea
                        label="Feedback"
                        placeholder="Share your thoughts..."
                        helperText="Please provide constructive feedback"
                      />
                    </div>
                  ),
                  description:
                    "Use the help text description to convey requirements or to show any formatting examples that would help user comprehension.",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <Textarea
                        label="Feedback"
                        placeholder="Share your thoughts..."
                        helperText="For example: Share your thoughts... We'll use this to improve our product. Please provide detailed and constructive feedback about your experience."
                      />
                    </div>
                  ),
                  description:
                    "Helper text should be 2 lines or less and avoid repeating the field label. If the field label provides sufficient context for completing the action, then you likely don't need to add help text.",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    size: {
      control: { type: "radio" },
      options: ["small", "medium"],
      description: "Visual size of the textarea. Default is 'medium'.",
      table: {
        type: { summary: '"small" | "medium"' },
        defaultValue: { summary: "medium" },
      },
    },
    label: {
      control: { type: "text" },
      description: "Accessible label displayed above the textarea.",
    },
    helperText: {
      control: { type: "text" },
      description: "Helper or error text displayed below the textarea.",
    },
    placeholder: {
      control: { type: "text" },
      description: "Placeholder text for the textarea element.",
    },
    error: {
      control: { type: "boolean" },
      description:
        "When true, shows error styles and treats helperText as an error message.",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables the textarea.",
    },
    required: {
      control: { type: "boolean" },
      description:
        "Marks the field as required and shows an asterisk in the label.",
    },
    fullWidth: {
      control: { type: "boolean" },
      description: "Stretches the textarea to the full width of its container.",
    },
    hasCharacterCount: {
      control: { type: "boolean" },
      description:
        "Displays a character count in the helper area; pair with maxLength.",
    },
    maxLength: {
      control: { type: "number" },
      description:
        "Maximum number of characters allowed in the textarea (enables counter when hasCharacterCount is true).",
      table: { type: { summary: "number" } },
    },
    minRows: {
      control: { type: "number" },
      description: "Minimum number of rows for the textarea. Default is 3.",
      table: { type: { summary: "number" }, defaultValue: { summary: "3" } },
    },
    maxRows: {
      control: { type: "number" },
      description: "Maximum number of rows for auto-resizing textarea.",
      table: { type: { summary: "number" } },
    },
    rows: {
      control: { type: "number" },
      description:
        "Fixed number of rows (overrides minRows and maxRows for fixed height).",
      table: { type: { summary: "number" } },
    },
    labelDecorator: {
      control: { type: "text" },
      description:
        "Element displayed alongside the label (e.g., badge, helper chip, or icon). Accepts ReactNode.",
      table: { type: { summary: "ReactNode" } },
    },
    helperTextDecorator: {
      control: { type: "text" },
      description:
        "Element displayed alongside the helper text (e.g., info icon, suffix). Accepts ReactNode.",
      table: { type: { summary: "ReactNode" } },
    },
    onChange: {
      control: false,
      description: "Callback fired when the value changes.",
      table: {
        type: {
          summary: "(event: React.ChangeEvent<HTMLTextAreaElement>) => void",
        },
      },
    },
    rootRef: {
      control: false,
      description: "Ref for the root element.",
      table: { type: { summary: "Ref<HTMLDivElement>" } },
    },
    rootProps: {
      control: false,
      description: "Props for the root element.",
      table: { type: { summary: "HTMLAttributes<HTMLDivElement>" } },
    },
    fieldProps: {
      control: false,
      description: "Props for the field element.",
      table: { type: { summary: "FieldProps" } },
    },
  },
  args: {
    label: undefined,
    helperText: undefined,
    placeholder: "Enter your text here...",
    error: false,
    disabled: false,
    required: false,
    fullWidth: false,
    hasCharacterCount: false,
    minRows: 3,
  },
} satisfies Meta<typeof Textarea>;

export default meta;

type Story = StoryObj<typeof Textarea>;

/** Default Textarea (demonstrates default size 'medium') */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview Textarea without explicit size prop. The component defaults to size 'medium' with 3 rows.",
      },
    },
  },
  args: {
    label: "Description",
    helperText: "Helper text",
    hasCharacterCount: true,
    maxLength: 10,
  },
};

/** Textarea with different sizes (small, medium) */
export const Sizes: Story = {
  parameters: {
    docs: {
      description: {
        story: "Showcases both available sizes: small and medium (default).",
      },
    },
  },
  render: (args) => (
    <div style={{ display: "flex", gap: 16, alignItems: "flex-start" }}>
      <div>
        <Textarea
          {...args}
          size="small"
          label="Small"
          placeholder="Small size"
        />
      </div>
      <div>
        <Textarea
          {...args}
          label="Medium (Default)"
          placeholder="Medium size"
        />
      </div>
    </div>
  ),
};

/** Textarea with full width */
export const FullWidth: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story: "Textarea stretches to fill the width of its container.",
      },
    },
  },
  render: (args) => (
    <div style={{ width: "100%" }}>
      <Textarea {...args} fullWidth label="Full width" />
    </div>
  ),
};

/** Textarea with different row configurations */
export const RowConfigurations: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates different row configurations: fixed rows, minRows/maxRows for auto-resizing.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(3, 1fr)",
          gap: 20,
        }}
      >
        <Textarea
          label="Fixed 2 rows"
          placeholder="This textarea has exactly 2 rows"
          rows={2}
        />
        <Textarea
          label="Auto-resize (3-6 rows)"
          placeholder="This textarea auto-resizes from 3 to 6 rows"
          minRows={3}
          maxRows={6}
        />
        <Textarea
          label="Large (5 rows)"
          placeholder="This textarea has 5 rows"
          rows={5}
        />
      </div>
    </div>
  ),
};

/** Comprehensive states showcase */
export const States: Story = {
  parameters: {
    // layout: "padded",
    docs: {
      description: {
        story:
          "A side-by-side comparison of common Textarea states for quick visual reference: default, disabled, error, helper text, and with character count.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, minmax(250px, 1fr))",
          gap: 20,
          alignItems: "start",
        }}
      >
        <Textarea placeholder="Default" />
        <Textarea
          label="Description"
          labelDecorator={<InfoCircle size={12} />}
          placeholder="With field label & label decorator"
        />
        <Textarea
          helperText="Helper text"
          helperTextDecorator={<InfoCircle size={12} />}
          placeholder="With helper text & helper text decorator"
        />
        <Textarea
          hasCharacterCount
          maxLength={50}
          placeholder="With character count"
        />
        <Textarea disabled placeholder="Disabled with placeholder" />
        <Textarea disabled value={"Disabled with value"} />
        <Textarea error helperText="Error message" placeholder="With error" />
        <Textarea
          error
          helperText="Error message"
          helperTextDecorator={<InfoCircle size={12} />}
          placeholder="With error & helper text decorator"
        />
      </div>
    );
  },
};

/** Textarea with label */
export const Label: Story = {
  parameters: {
    docs: {
      description: {
        story: "Textarea with label and label decorator.",
      },
    },
  },
  render: () => {
    function Demo() {
      const [open, setOpen] = useState(false);
      const close = () => setOpen(false);
      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "center",
          }}
        >
          <Typography level="bodyLarge">Default</Typography>
          <Textarea label="Comments" placeholder="Enter your comments" />
          <Typography level="bodyLarge">Label Decorator (Clickable)</Typography>
          <Textarea
            label="Feedback"
            placeholder="Enter your feedback"
            labelDecorator={
              <IconButton
                type="button"
                onClick={() => setOpen(true)}
                style={{
                  padding: 0,
                  background: "none",
                  height: "fit-content",
                  minHeight: "fit-content",
                  width: "fit-content",
                  minWidth: "fit-content",
                }}
                aria-label="More info"
                size="small"
              >
                <InfoCircle size={12} />
              </IconButton>
            }
          />

          <Modal.Root
            open={open}
            onOpenChange={(o) => setOpen(!!o)}
            dismissible
          >
            <Modal.Header>Modal Title</Modal.Header>
            <Modal.Content>
              <div>
                Once upon a time, there was a forest where plenty of birds lived
                and built their nests on the trees.
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button onClick={close}>Button</Button>
            </Modal.Footer>
            <Modal.CloseButton />
          </Modal.Root>
        </div>
      );
    }
    return <Demo />;
  },
};

/** Textarea with helper text */
export const HelperText: Story = {
  parameters: {
    docs: {
      description: {
        story: "Textarea with helper text and helper text decorator.",
      },
    },
  },
  render: () => {
    function Demo() {
      const [open, setOpen] = useState(false);
      const close = () => setOpen(false);
      return (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(2, minmax(300px, 1fr))",
            gap: 20,
            alignItems: "center",
          }}
        >
          <Typography level="bodyLarge">Default</Typography>
          <Textarea
            label="Description"
            helperText="Provide a detailed description"
            placeholder="Enter description"
          />
          <Typography level="bodyLarge">With long helper text</Typography>
          <Textarea
            label="Feedback"
            placeholder="Enter your feedback"
            error
            helperText={
              <Typography level="labelLarge" color="negative">
                This field is required
                <Typography
                  level="labelLarge"
                  color="negative"
                  onClick={() => setOpen(true)}
                  style={{
                    cursor: "pointer",
                    textDecoration: "underline",
                    marginLeft: "4px",
                  }}
                >
                  show detail
                </Typography>
              </Typography>
            }
          />
          <Modal.Root
            open={open}
            onOpenChange={(o) => setOpen(!!o)}
            dismissible
          >
            <Modal.Header>Modal Title</Modal.Header>
            <Modal.Content>
              <div>
                Once upon a time, there was a forest where plenty of birds lived
                and built their nests on the trees.
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button onClick={close}>Button</Button>
            </Modal.Footer>
            <Modal.CloseButton />
          </Modal.Root>
        </div>
      );
    }
    return <Demo />;
  },
};

/** Textarea with validation */
export const Validation: Story = {
  parameters: {
    docs: {
      description: {
        story: "Textarea with label, helper text, and validation states.",
      },
    },
  },
  args: {
    label: "Comments",
    helperText: "This field is required",
    error: true,
  },
};

/** Textarea with required field indicator */
export const RequiredTextarea: Story = {
  parameters: {
    docs: {
      description: { story: "Shows the required asterisk next to the label." },
    },
  },
  args: {
    label: "Feedback",
    placeholder: "Enter your feedback",
    required: true,
  },
};

/** Textarea with character count */
export const CharacterCount: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates character counting functionality with different limits.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",
          gap: 20,
        }}
      >
        <Textarea
          label="Short comment (50 chars)"
          placeholder="Keep it brief"
          hasCharacterCount
          maxLength={50}
          rows={2}
        />
        <Textarea
          label="Medium description (200 chars)"
          placeholder="Provide more details"
          hasCharacterCount
          maxLength={200}
          minRows={3}
          maxRows={5}
        />
      </div>
      <Textarea
        label="Long form content (500 chars)"
        placeholder="Write a detailed explanation"
        hasCharacterCount
        maxLength={500}
        minRows={4}
        maxRows={8}
        fullWidth
      />
    </div>
  ),
};

/** Auto-resizing behavior demonstration */
export const AutoResize: Story = {
  parameters: {
    docs: {
      description: {
        story: "Demonstrates auto-resizing behavior as content grows.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 20 }}>
      <Typography level="bodyLarge">
        Type in the textareas below to see auto-resizing in action:
      </Typography>
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(2, 1fr)",
          gap: 20,
        }}
      >
        <Textarea
          label="Auto-resize (2-6 rows)"
          placeholder="Start typing to see the textarea grow..."
          minRows={2}
          maxRows={6}
          helperText="Grows from 2 to 6 rows"
        />
        <Textarea
          label="Auto-resize (3-8 rows)"
          placeholder="This one has a larger range..."
          minRows={3}
          maxRows={8}
          helperText="Grows from 3 to 8 rows"
        />
      </div>
    </div>
  ),
};


/** Textarea in form */
export const TextareaInForm: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "Comprehensive form example showing how Textarea integrates with other form components in a real-world support ticket submission form. Demonstrates validation, different textarea configurations, and proper form structure.",
      },
    },
  },
  render: () => {
    function SupportTicketForm() {
      const [formData, setFormData] = useState({
        name: "",
        email: "",
        priority: "",
        category: "",
        subject: "",
        description: "",
      });
      const [errors, setErrors] = useState<Record<string, string>>({});
      const [isSubmitting, setIsSubmitting] = useState(false);

      const handleInputChange = (field: string, value: string | boolean) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
          setErrors((prev) => ({ ...prev, [field]: "" }));
        }
      };

      const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.name.trim()) newErrors.name = "Name is required";
        if (!formData.email.trim()) newErrors.email = "Email is required";
        else if (!/\S+@\S+\.\S+/.test(formData.email))
          newErrors.email = "Please enter a valid email";
        if (!formData.priority)
          newErrors.priority = "Please select a priority level";
        if (!formData.category) newErrors.category = "Please select a category";
        if (!formData.subject.trim()) newErrors.subject = "Subject is required";
        else if (formData.subject.length < 10)
          newErrors.subject = "Subject must be at least 10 characters";

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
      };

      const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validateForm()) {
          setIsSubmitting(true);
          // Simulate API call
          setTimeout(() => {
            setIsSubmitting(false);
            alert("Support ticket submitted successfully!");
          }, 2000);
        }
      };

      return (
        <div style={{ maxWidth: 800, margin: "0 auto", padding: 20 }}>
          <div style={{ marginBottom: 32 }}>
            <Typography level="titleLarge" style={{ marginBottom: 8 }}>
              Submit Support Ticket
            </Typography>
            <Typography level="bodyMedium" color="tertiary">
              Please provide detailed information about your issue to help us
              assist you better.
            </Typography>
          </div>

          <form
            onSubmit={handleSubmit}
            style={{ display: "flex", flexDirection: "column", gap: 24 }}
          >
            {/* Contact Information Section */}
            <fieldset
              style={{
                border: "1px solid #e5e7eb",
                borderRadius: 8,
                padding: 20,
                margin: 0,
              }}
            >
              <legend style={{ padding: "0 8px", fontWeight: 600 }}>
                Contact Information
              </legend>
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: 16,
                  marginTop: 16,
                }}
              >
                <Input
                  label="Full Name"
                  placeholder="Enter your full name"
                  required
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  error={!!errors.name}
                  helperText={errors.name}
                />
                <Input
                  label="Email Address"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  error={!!errors.email}
                  helperText={errors.email}
                />
              </div>
            </fieldset>

            {/* Issue Details Section */}
            <fieldset
              style={{
                border: "1px solid #e5e7eb",
                borderRadius: 8,
                padding: 20,
                margin: 0,
              }}
            >
              <legend style={{ padding: "0 8px", fontWeight: 600 }}>
                Issue Details
              </legend>
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 16,
                  marginTop: 16,
                }}
              >
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr",
                    gap: 16,
                  }}
                >
                  <Select
                    label="Priority Level"
                    placeholder="Select priority"
                    required
                    value={formData.priority}
                    onChange={(value) => handleInputChange("priority", value)}
                    error={!!errors.priority}
                    helperText={errors.priority}
                    fullWidth
                  >
                    <Select.Option label="Low" value="low" />
                    <Select.Option label="Medium" value="medium" />
                    <Select.Option label="High" value="high" />
                    <Select.Option label="Critical" value="critical" />
                  </Select>
                  <Select
                    label="Category"
                    placeholder="Select category"
                    required
                    value={formData.category}
                    onChange={(value) => handleInputChange("category", value)}
                    error={!!errors.category}
                    helperText={errors.category}
                    fullWidth
                  >
                    <Select.Option label="Bug Report" value="bug" />
                    <Select.Option label="Feature Request" value="feature" />
                    <Select.Option label="Technical Support" value="support" />
                    <Select.Option label="Account Issue" value="account" />
                    <Select.Option label="Other" value="other" />
                  </Select>
                </div>

                <Textarea
                  label="Subject"
                  placeholder="Brief summary of the issue"
                  required
                  rows={2}
                  maxLength={10}
                  hasCharacterCount
                  value={formData.subject}
                  onChange={(e) => handleInputChange("subject", (e.target as HTMLInputElement | HTMLTextAreaElement).value)}
                  error={!!errors.subject}
                  helperText={
                    errors.subject || "Provide a clear, concise subject line"
                  }
                  fullWidth
                />

                <Textarea
                  label="Detailed Description"
                  placeholder="Please describe the issue in detail. Include what you were trying to do, what happened, and what you expected to happen."
                  minRows={4}
                  maxRows={8}
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", (e.target as HTMLInputElement | HTMLTextAreaElement).value)
                  }
                  helperText={
                    errors.description ||
                    "The more details you provide, the better we can help you"
                  }
                  fullWidth
                />
              </div>
            </fieldset>
            {/* Submit Button */}
            <div
              style={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 12,
                paddingTop: 16,
              }}
            >
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  setFormData({
                    name: "",
                    email: "",
                    priority: "",
                    category: "",
                    subject: "",
                    description: "",
                  });
                  setErrors({});
                }}
              >
                Clear Form
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Submit Ticket"}
              </Button>
            </div>
          </form>
        </div>
      );
    }

    return <SupportTicketForm />;
  },
};

